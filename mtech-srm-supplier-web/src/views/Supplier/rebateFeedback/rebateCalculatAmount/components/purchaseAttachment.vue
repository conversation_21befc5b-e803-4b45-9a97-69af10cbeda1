<template>
  <!-- 采方附件 -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      :columns="columns"
      :table-data="tableData"
      height="180"
      :fix-height="180"
      :auto-height="false"
      :loading="loading"
      :row-config="{ height: 45 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download } from '@/utils/utils'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'purcahseAttachment'
    }
  },
  computed: {},
  mounted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const _selectRows = this.tableRef.getCheckboxRecords()
      if (['download'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'download':
          _selectRows.forEach((row) => this.handleDownload(row))
          break
        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload(row)
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field == 'fileName') {
        let params = {
          id: row?.sysFileId || row?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 下载
    handleDownload(row) {
      this.$API.fileService.downloadPrivateFile({ id: row.sysFileId }).then((res) => {
        download({ fileName: row.fileName, blob: new Blob([res.data]) })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.cell-btn {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
  margin-right: 10px;
}
</style>
