<template>
  <!-- 采方附件 -->
  <div class="detail-table">
    <ScTable
      ref="scTableRef"
      :columns="columns"
      :table-data="tableData"
      :height="tableHeight"
      :fix-height="tableHeight"
      :is-show-right-btn="false"
      :auto-height="false"
      :loading="loading"
      :row-config="{ height: 48 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download } from '@/utils/utils'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'purcahseAttachment'
    }
  },
  computed: {
    dataList() {
      const dataList = []
      this.tableData.forEach((item) => {
        item.id = this.$route.query.id || null
        dataList.push(item)
      })
      return dataList
    },
    // 固定表格高度 - 与其他附件组件保持一致
    tableHeight() {
      // 固定高度，禁用ScTable的自动高度计算
      return 200
    }
  },
  mounted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const _selectRows = this.tableRef.getCheckboxRecords()
      if (['download', 'delete'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请至少先选择一行数据'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'upload':
          this.handleUpload()
          break
        case 'download':
          _selectRows.forEach((row) => this.handleDownload(row))
          break
        case 'delete':
          this.handleDelete()
          break
        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload(row)
          break
        case 'delete':
          this.handleDelete(row)
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field == 'attachmentName') {
        let params = {
          id: row?.attachmentId,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        })
      }
    },
    // 上传文件
    handleUpload() {
      this.$dialog({
        modal: () => import('./uploadDialog.vue'),
        data: {
          title: this.$t('上传')
        },
        success: (data) => {
          let { id, fileName, fileSize, fileType, url, createTime } = data
          const userInfo = JSON.parse(sessionStorage.getItem('userInfo')) || {}
          let newRow = {
            id: this.$route.query.id,
            attachmentId: id,
            attachmentName: fileName,
            attachmentSize: fileSize,
            attachmentType: fileType,
            attachmentUrl: url,
            uploadUserName: userInfo?.username,
            uploadTime: createTime ? new Date(createTime).getTime() : null
          }
          this.tableData.push(newRow)
        }
      })
    },
    //删除
    handleDelete(row) {
      this.$dialog({
        data: {
          title: this.$t('提示'),
          message: this.$t('确认删除选中的数据？')
        },
        success: async () => {
          row ? await this.tableRef.remove(row) : await this.tableRef.removeCheckboxRow()
          this.tableData = this.tableRef.getTableData().fullData
        }
      })
    },
    // 下载
    handleDownload(row) {
      this.$API.fileService.downloadPrivateFile({ id: row.attachmentId }).then((res) => {
        download({ fileName: row.attachmentName, blob: new Blob([res.data]) })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.detail-table {
  ::v-deep .vxe-table {
    .vxe-body--row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.cell-btn {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
  margin-right: 10px;
  text-decoration: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #66b1ff;
    background-color: #f0f8ff;
  }

  i {
    font-size: 12px;
  }
}
</style>
