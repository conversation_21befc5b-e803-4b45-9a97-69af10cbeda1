<template>
  <!-- 供方附件 -->
  <div class="detail-table">
    <ScTable
      ref="sctableRef"
      :columns="columns"
      :table-data="tableData"
      :height="tableHeight"
      :fix-height="tableHeight"
      :auto-height="false"
      :is-show-right-btn="false"
      :loading="loading"
      :row-config="{ height: 48 }"
    >
      <template slot="custom-tools">
        <vxe-button
          v-for="item in toolbar"
          v-show="!item.isHidden"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </ScTable>
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import mixin from './config/mixin'
import { download } from '@/utils/utils'

export default {
  components: { ScTable },
  mixins: [mixin],
  data() {
    return {
      type: 'supplierAttachment'
    }
  },
  computed: {
    // 固定表格高度 - 与其他附件组件保持一致
    tableHeight() {
      // 固定高度，禁用ScTable的自动高度计算
      return 200
    }
  },
  mounted() {},
  methods: {
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const _selectRows = this.tableRef.getCheckboxRecords()
      if (['download'].includes(e.code) && !_selectRows.length) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (e.code) {
        case 'download':
          _selectRows.forEach((row) => this.handleDownload(row))
          break
        default:
          break
      }
    },
    // 单元格按钮点击
    handleClickCellTool(code, row) {
      switch (code) {
        case 'download':
          this.handleDownload(row)
          break
        default:
          break
      }
    },
    // 单元格title文字点击
    handleClickCellTitle(row, column) {
      if (column.field == 'fileName') {
        let params = {
          id: row?.sysFileId || row?.id,
          useType: 2
        }
        this.$API.fileService.getMtPreview(params).then((res) => {
          window.open(res.data)
        }).catch((error) => {
          console.error('预览文件失败:', error)
          this.$toast({ content: this.$t('预览文件失败'), type: 'error' })
        })
      }
    },
    // 下载
    handleDownload(row) {
      this.$API.fileService.downloadPrivateFile({ id: row.sysFileId }).then((res) => {
        download({ fileName: row.fileName, blob: new Blob([res.data]) })
      }).catch((error) => {
        console.error('下载文件失败:', error)
        this.$toast({ content: this.$t('下载文件失败'), type: 'error' })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.detail-table {
  ::v-deep .vxe-table {
    .vxe-body--row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }
}

.cell-btn {
  color: #409eff;
  font-size: 12px;
  font-weight: 400;
  margin-right: 10px;
  text-decoration: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    color: #66b1ff;
    background-color: #f0f8ff;
  }

  i {
    font-size: 12px;
  }
}
</style>
