/********** 成本因子联动定价 ***********/

import { API } from '@mtech-common/http'
import { PROXY_SOURCING } from 'CONFIG/proxy.config'

const NAME = 'costCalculation'
const APIS = {
  // 查询列表-查询
  queryCostCalculationList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/pageQuery`, data)
  },
  // 查询列表-导出
  exportCostCalculationList: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/export`, data, {
      responseType: 'blob'
    })
  },
  // 查询列表-批量删除
  deleteCostCalculation: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/batchDel`, data)
  },
  // 查询列表-批量提交
  batchSubmitCostCalculation: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/batchCommit`, data)
  },
  // 详情页 - 查询 - 头部
  queryHeader: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/queryHeader`, data)
  },
  // 详情页 - 保存 - 头部
  saveCostCalculation: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/save`, data)
  },
  // 详情页 - 提交 - 头部
  submitCostCalculation: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/commit`, data)
  },
  // 详情页 - 查询 - 明细
  costItemPageQuery: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/itemPageQuery`, data)
  },
  // 详情页 - 保存 - 明细
  saveCostItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/saveItem`, data)
  },
  // 详情页 - 删除 - 明细
  deleteCostItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/delItem`, data)
  },
  // 详情页 - 根据rfxid查询所有文件节点信息
  queryFileNodeByRfxId: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/kt/cost-model/queryFileNodeByRfxId`, data)
  },
  // 详情页 - 根据rfxid查询所有文件信息
  queryFileByRfxId: `${PROXY_SOURCING}/tenant/kt/cost-model/queryFileByRfxId`,
  // 详情页 - 保存文件信息
  saveFile: (data = {}) => {
    return API.put(`${PROXY_SOURCING}/tenant/kt/cost-model/saveFile`, data)
  },
  // 详情页 - 下载导入模板 - 明细
  downloadImportTemplate: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/downloadImportTemplate`, data, {
      responseType: 'blob'
    }),
  // 详情页 - 导入 - 明细
  importCostItem: (data = {}) =>
    API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/importItem`, data, {
      responseType: 'blob'
    }),
  // 详情页 - 导出 - 明细
  exportCostItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/exportItem`, data, {
      responseType: 'blob'
    })
  },
  // 详情页 - 查询 - 根据公司、采购组、供应商查币种税率信息
  queryTaxRate: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/query-currency-tax-rate`, data)
  },
  // 详情页 - 根据单号+物料查询历史最近价格记录
  queryLatestPriceRecord: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/queryLatestPriceRecord`, data)
  },
  // 详情页 - 根据公司编码、采购组织编码、供应商编码，品类、获取报价属性、价格生效方式
  queryDefaultValue: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/query-default-value`, data)
  },
  // 详情页 - 获取LT
  getPlanTime: (data = {}) => {
    return API.get(`${PROXY_SOURCING}/tenant/kt/cost-model/getPlanTime`, data)
  },
  // 测算页 - 查询成本测算明细信息
  queryItem: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/queryItem`, data)
  },
  // 测算页 - 查询采方成本模型测算
  queryEstimate: (data = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/queryEstimate`, data)
  },
  // 测算页-查询历史成本模型测算
  queryHistoryEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/historyEstimate`, params)
  },
  // 测算页-查询采购明细成本模型 待替换
  queryRfxItemCostModel: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/queryRfxItemCostModel`, params)
  },
  // 采方-保存采购明细成本模型待替换
  saveRfxItemCostModel: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/rfxItem/costModel/saveRfxItemCostModel`, params)
  },
  // 测算页-保存成本测算
  saveEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/saveEstimate`, params)
  },
  // 测算页-提交成本测算
  submitEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/commitEstimate`, params)
  },
  // 测算页- 导入成本模型测算
  importEstimate: (data = {}) => {
    return API.post(
      `${PROXY_SOURCING}/tenant/kt/cost-model/rfxItem/importItem?id=${data.id}`,
      data.data,
      {
        responseType: 'blob'
      }
    )
  },
  // 测算页-导出成本模型测算
  exportEstimate: (data = {}) =>
    API.get(`${PROXY_SOURCING}/tenant/kt/cost-model/rfxItem/exportItem`, data, {
      responseType: 'blob'
    }),
  // 测算页-成本测算
  costEstimate: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/estimate`, params)
  },
  // 采方-根据物料编码查询MDM因子属性
  queryMdmProperty: (params = {}) => {
    return API.post(`${PROXY_SOURCING}/tenant/kt/cost-model/queryMdmProperty`, params)
  }
}

export default {
  NAME,
  APIS
}