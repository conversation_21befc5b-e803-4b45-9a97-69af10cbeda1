<template>
  <mt-dialog
    ref="dialog"
    css-class="mdm-property-dialog"
    :header="$t('查看因子属性')"
    :buttons="buttons"
    width="1200"
    height="600"
    @beforeClose="handleClose"
  >
    <div class="dialog-content">
      <!-- 基本信息 -->
      <div class="basic-info">
        <h4>{{ $t('基本信息') }}</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>{{ $t('物料编码') }}：</label>
            <span>{{ material.materialCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>{{ $t('物料名称') }}：</label>
            <span>{{ material.materialName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>{{ $t('分类编码') }}：</label>
            <span>{{ material.categoryCode || '-' }}</span>
          </div>
          <div class="info-item">
            <label>{{ $t('类别名称') }}：</label>
            <span>{{ material.categoryName || '-' }}</span>
          </div>
          <div class="info-item">
            <label>{{ $t('基本计量单位') }}：</label>
            <span>{{ material.basicUnit || '-' }}</span>
          </div>
          <div class="info-item">
            <label>{{ $t('物料类型') }}：</label>
            <span>{{ material.materialType || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 动态属性表格 -->
      <div class="property-table">
        <h4>{{ $t('因子属性') }}</h4>
        <div v-if="tableData.length === 0" class="no-data">
          <i class="el-icon-info"></i>
          <span>{{ $t('暂无因子属性数据') }}</span>
        </div>
        <sc-table
          v-else
          ref="propertyTable"
          :columns="columns"
          :table-data="tableData"
          :loading="false"
          :is-show-refresh-bth="false"
          :is-show-right-btn="false"
          :fix-height="300"
        />
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'

export default {
  name: 'MdmPropertyDialog',
  components: {
    ScTable
  },
  props: {
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { isPrimary: 'true', content: this.$t('关闭') }
        }
      ],
      material: {},
      headerList: [],
      columns: [],
      tableData: []
    }
  },
  mounted() {
    this.initDialog()
  },
  methods: {
    initDialog() {
      // 显示弹框
      this.$refs.dialog.ejsRef.show()

      // 初始化数据
      if (this.modalData && this.modalData.data) {
        const { material = {}, headerList = [] } = this.modalData.data
        this.material = material
        this.headerList = headerList
        this.initTable()
      }
    },

    initTable() {
      // 构建表格列
      this.columns = [
        {
          field: 'name',
          title: this.$t('属性名称'),
          minWidth: 200,
          align: 'left'
        },
        {
          field: 'field',
          title: this.$t('字段属性'),
          minWidth: 150,
          align: 'left'
        },
        {
          field: 'value',
          title: this.$t('属性值'),
          minWidth: 200,
          align: 'left',
          slots: {
            default: ({ row }) => {
              const value = row.value || '-'
              return [<span title={value}>{value}</span>]
            }
          }
        }
      ]

      // 构建表格数据
      this.tableData = this.headerList.map(item => {
        const fieldValue = this.material[item.field]
        let displayValue = '-'

        // 处理不同类型的值
        if (fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
          if (typeof fieldValue === 'object') {
            displayValue = JSON.stringify(fieldValue)
          } else {
            displayValue = String(fieldValue)
          }
        }

        return {
          name: item.name || '-',
          field: item.field || '-',
          value: displayValue
        }
      })
    },

    handleClose() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-content {
  padding: 20px;

  .basic-info {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: bold;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;

      .info-item {
        display: flex;
        align-items: center;

        label {
          font-weight: bold;
          color: #666;
          min-width: 100px;
          margin-right: 8px;
        }

        span {
          color: #333;
          word-break: break-all;
        }
      }
    }
  }

  .property-table {
    h4 {
      margin: 0 0 15px 0;
      color: #333;
      font-size: 16px;
      font-weight: bold;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .no-data {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #999;
      font-size: 14px;

      i {
        margin-right: 8px;
        font-size: 18px;
      }
    }
  }
}

::v-deep .mdm-property-dialog {
  .mt-dialog-content {
    padding: 0;
  }
}
</style>
