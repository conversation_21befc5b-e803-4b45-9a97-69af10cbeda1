export default {
  data() {
    return {
      costDetailList: [],
      toolbar: [
        { code: 'import', name: this.$t('导入'), status: 'info' },
        { code: 'export', name: this.$t('导出'), status: 'info' },
        { code: 'viewFactor', name: this.$t('查看因子属性'), status: 'info' }
      ]
    }
  },
  computed: {
    defaultColumns() {
      return [
        {
          width: 50,
          fixed: this.enableCalculate ? 'left' : '',
          slots: {
            default: () => {
              return [
                <span class='drag-btn'>
                  <vxe-button type='text' icon='vxe-icon-menu' />
                </span>
              ]
            },
            header: () => {
              return [
                <vxe-tooltip content='按住后可以上下拖动排序！' enterable>
                  <vxe-button type='text' icon='vxe-icon-question-circle-fill' />
                </vxe-tooltip>
              ]
            }
          }
        },
        {
          field: 'nodeName',
          title: this.$t('成本构成'),
          fixed: this.enableCalculate ? 'left' : '',
          minWidth: 180,
          treeNode: true,
          editRender: {
            enabled: !this.enableCalculate
          },
          slots: {
            edit: ({ row, level }) => {
              let optionList = this.costDetailList

              // 二级成本明细列表，是一级明细的子集
              let parentRow = {}
              if (level > 0) {
                parentRow = this.findTreeDirectParentNode(this.tableData, row.id)
                optionList = parentRow?.levelTwoOptionsList || []
                if (level === 1 && !optionList.length && parentRow) {
                  const item = this.costDetailList.find((t) => t.itemName === parentRow.nodeName)
                  optionList = item?.children || []
                }
              }
              return [
                <div>
                  <vxe-select
                    v-show={level < 2}
                    v-model={row.nodeName}
                    options={optionList}
                    option-props={{ value: 'itemName', label: 'itemName' }}
                    placeholder={this.$t('请选择')}
                    transfer
                    size='mini'
                    clearable
                    filterable
                    disabled={!row.isAdd}
                    onChange={(e) => {
                      const selectItem = optionList.find((t) => t.itemName === e.value)
                      row.nodeCode = selectItem.itemCode

                      if (level === 0) {
                        // 选择一级成本明细后，保存二级选项
                        row.levelTwoOptionsList = selectItem.children || []
                      }
                    }}
                  />
                  <div v-show={level >= 2} style='display: flex'>
                    <vxe-input v-model={row.nodeName} clearable />
                  </div>
                </div>
              ]
            }
          }
        },
        {
          type: 'seq',
          title: this.$t('排序'),
          width: 80,
          slots: {
            default: ({ row, seq }) => {
              row.sortCode = seq
              return [<span>{seq}</span>]
            }
          }
        },
        {
          field: 'calculationFormulaSpec',
          title: this.$t('计算公式'),
          minWidth: 200
        },
        {
          field: 'remark',
          title: this.$t('备注'),
          minWidth: 120,
          editRender: {
            enabled: !this.enableCalculate
          },
          slots: {
            edit: ({ row }) => {
              return [<vxe-input v-model={row.remark} clearable />]
            }
          }
        },
        {
          field: 'result',
          title: this.$t('成本'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.result}
                  </span>
                </div>
              ]
            }
          }
        },
        {
          field: 'percent',
          title: this.$t('占销价百分比（%）'),
          minWidth: 180,
          slots: {
            default: ({ row, level }) => {
              let tip = ''
              if (level === 0) {
                tip = this.$t('合计：')
              } else if (row.itemList?.length) {
                tip = this.$t('小计：')
              }
              return [
                <div>
                  <span>
                    {tip}
                    {row.percent}
                  </span>
                </div>
              ]
            }
          }
        }
      ]
    }
  },
  methods: {
    // 获取成本明细下拉列表
    async getCostModelLevelList() {
      const res = await this.$API.masterData.dictionaryGetTreeList({
        dictCode: 'COST_MODEL_ITEM_LEVEL_TREE'
      })
      if (res.code === 200) {
        this.costDetailList = res.data || []
      }
    },
    // 新增行
    async handleAddRow(curRow) {
      // 生成18位的数字id
      const id = Math.floor((Math.random() + Math.floor(Math.random() * 9 + 1)) * Math.pow(10, 17))
      const newRow = { id, isAdd: true }
      const newTabelData = this.addTreeNode(this.tableData, curRow.id, newRow)
      this.tableData = [...newTabelData]

      await this.tableRef.setTreeExpand(curRow, true) // 将父节点展开
      await this.tableRef.setEditRow(newRow) // 设置行为编辑状态
    },
    // 编辑行
    async handleUpdateRow(curRow, type) {
      const tableData = [...this.tableData]
      this.updateTreeNode(tableData, curRow.id, curRow)
      this.tableData = [...tableData]

      if (type !== 'parent') {
        await this.tableRef.setEditRow(curRow) // 设置行为编辑状态
      }
    },
    // 数组（树结构）-- 新增
    addTreeNode(treeList, id, obj) {
      treeList.forEach((ele) => {
        if (ele.id === id) {
          ele.itemList ? ele.itemList.unshift(obj) : (ele.itemList = [obj])
        } else {
          if (ele.itemList) {
            this.addTreeNode(ele.itemList, id, obj)
          }
        }
      })
      return treeList
    },
    // 数组（树结构）-- 修改
    updateTreeNode(treeList, id, obj) {
      if (!treeList || !treeList.length) {
        return
      }
      for (let i = 0; i < treeList.length; i++) {
        if (treeList[i].id == id) {
          treeList[i] = obj
          break
        }
        this.updateTreeNode(treeList[i].itemList, id, obj)
      }
    },
    // 数组（树结构）-- 查找直接父结点
    findTreeDirectParentNode(treeList, id) {
      for (let node of treeList) {
        if (node.itemList) {
          const flag = node.itemList.some((child) => child.id === id)
          if (flag) {
            return node
          }
          const parent = this.findTreeDirectParentNode(node.itemList || [], id)
          if (parent) {
            return parent
          }
        }
      }
      return null
    }
  }
}
