<template>
  <div class="detail-content">
    <mt-form
      v-if="enableCalculate"
      style="margin: 10px 0 0 5px; width: calc(33.33% - 10px)"
      ref="tempFormRef"
      :model="tempForm"
    >
      <mt-form-item prop="historyCount" :label="$t('历史测算')" label-style="left">
        <vxe-select
          v-model="tempForm.historyCount"
          :options="historyCountList || []"
          :option-props="{ label: 'text', value: 'value' }"
          clearable
          :placeholder="$t('请选择历史测算')"
          @change="handleHistoryCountChange"
        />
      </mt-form-item>
    </mt-form>
    <!-- 附件参数表单 -->
    <div class="toggle-container">
      <div v-if="extraFormItems.length" class="common-form">
        <div class="common-title">
          <span style="margin-right: 5px">{{ $t('结果公式') }}</span>
          <i
            v-if="showExtraForm"
            class="vxe-icon-arrow-up"
            @click="() => (showExtraForm = false)"
          />
          <i v-else class="vxe-icon-arrow-down" @click="() => (showExtraForm = true)" />
        </div>
        <div class="common-body" v-if="showExtraForm">
          <custom-form
            :data="extraFormData"
            :form-items="extraFormItems"
            type="extra"
            @change="(data) => handleFormValueChange('extra', data)"
          />
          <div class="extra-tip">{{ extraTipInfo }}</div>
        </div>
      </div>
      <!-- 通用参数表格 -->
      <div v-if="commonFormItems.length" class="common-form">
        <div class="common-title">
          <span style="margin-right: 5px">{{ $t('公共参数') }}</span>
          <i
            v-if="showCommonForm"
            class="vxe-icon-arrow-up"
            @click="() => (showCommonForm = false)"
          />
          <i v-else class="vxe-icon-arrow-down" @click="() => (showCommonForm = true)" />
        </div>
        <div class="common-body" v-if="showCommonForm">
          <custom-form
            :data="commonFormData"
            :form-items="commonFormItems"
            type="common"
            @change="(data) => handleFormValueChange('common', data)"
          />
        </div>
      </div>
    </div>
    <sc-table
      ref="sctableRef"
      class="sortable-tree-demo"
      row-id="id"
      :keep-source="true"
      :tree-config="treeConfig"
      :edit-config="editConfig"
      :tooltip-config="tooltipConfig"
      :is-show-refresh-bth="true"
      :loading="loading"
      :columns="columns"
      :table-data="tableData"
      @edit-closed="handleCalculate"
      @refresh="onRefresh"
    >
      <template slot="custom-tools">
        <vxe-button
          v-show="$route.query.type === 'calculation'"
          v-for="item in toolbar"
          :key="item.code"
          :status="item.status"
          :icon="item.icon"
          :disabled="item.disabled"
          size="small"
          @click="handleClickToolBar(item)"
          >{{ item.name }}</vxe-button
        >
      </template>
    </sc-table>
    <!-- 上传弹窗 -->
    <upload-excel-dialog
      ref="uploadExcelRef"
      :request-urls="requestUrls"
      file-key="file"
      @closeUploadExcel="showUploadExcel(false)"
      @upExcelConfirm="upExcelConfirm"
    />
  </div>
</template>

<script>
import ScTable from '@/components/ScTable/src/index.js'
import CustomForm from './components/customForm.vue'
import Sortable from 'sortablejs'
import XEUtils from 'xe-utils'

import costModelDetailMixin from './config/costModelDetailMixin.js'
import selectMixin from './config/selectMixin.js'
import cloneDeep from 'lodash/cloneDeep'
import { download, getHeadersFileName } from '@/utils/utils'

export default {
  components: {
    ScTable,
    CustomForm,
    uploadExcelDialog: require('@/components/Upload/uploadExcelDialog.vue').default
  },
  mixins: [costModelDetailMixin, selectMixin],
  props: {
    formObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      enableCalculate: false, // 是否启用成本测算
      loading: false,
      extraFormItems: [],
      commonFormItems: [],
      extraFormData: {},
      commonFormData: {},
      columns: [],
      dynamicColumns: [],
      tableData: [],
      treeConfig: {
        children: 'itemList',
        expandAll: true
      },
      editConfig: {
        trigger: 'click',
        mode: 'row',
        showStatus: true
      },
      tooltipConfig: {
        enterable: true,
        contentMethod: ({ column, row }) => {
          const { field } = column
          switch (field) {
            case 'nodeName':
              return row?.nodeName + '\n\n' + row?.nodeCode
            case 'calculationFormulaSpec':
              return row?.calculationFormulaSpec + '\n\n' + row?.calculationFormula
            default:
              // 其余的单元格展示默认的内容
              return null
          }
        }
      },
      sortable2: null,

      extraFormula: [],
      showExtraForm: true,
      showCommonForm: true,
      tempForm: {},
      historyCountList: [],
      historyDataList: [],
      isContentChange: false, // 内容是否修改
      resData: {},
      estimateRes: {},
      requestUrls: {}
    }
  },
  computed: {
    tableRef() {
      return this.$refs.sctableRef.$refs.xGrid
    },
    extraTipInfo() {
      const arr = []
      this.extraFormula.forEach((item) => {
        const { columnName, calculationFormulaSpec } = item
        const spec =
          !calculationFormulaSpec && calculationFormulaSpec !== 0 ? '' : calculationFormulaSpec
        spec && arr.push(columnName + '=' + spec)
      })
      return arr.join(';')
    }
  },
  watch: {
    formObject: {
      handler() {
        this.init()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
  },
  methods: {
    // 初始化
    init() {
      this.enableCalculate = this.$route.query.type === 'calculation'
      this.getCostModelLevelList()
      this.treeDrop()
      if (this.formObject.costModelId) {
        this.getCostModelDetailColumns()
        this.getDataList()
        this.enableCalculate && this.getDataList('history')
      }
    },
    // 刷新
    onRefresh() {
      if (this.tempForm.historyCount || this.tempForm.historyCount === 0) {
        return
      }
      this.getDataList()
    },
    // 选择历史测算
    handleHistoryCountChange(e) {
      if (!e.value && e.value !== 0) {
        this.getDataList()
      } else {
        const data = this.historyDataList[e.value]
        this.$emit('updateFormData', data || {})
        this.resData = data || {}
        this.extraFormData = this.serializeFormData(data?.extraList || [])
        this.commonFormData = this.serializeFormData(data?.formValueList || [], 'common')
        this.tableData = this.serializeItemList(data?.itemList || [])
        this.$nextTick(() => this.tableRef.setAllTreeExpand(true))
      }
    },
    // 表单数据修改
    handleFormValueChange(type, formData) {
      this[type + 'FormData'] = formData
      if (type === 'common') {
        this.handleCalculate({})
      }
      this.isContentChange = true
    },
    // 点击工具栏
    handleClickToolBar(e) {
      switch (e.code) {
        case 'import':
          this.handleImport()
          break
        case 'export':
          this.handleExport()
          break
        case 'viewFactor':
          this.handleViewFactor()
          break
        default:
          break
      }
    },
    // 查看因子属性
    async handleViewFactor() {
      const materialCode = this.formObject.materialCode
      const categoryCode = this.formObject.categoryCode
      const factoryCode = this.formObject.factoryCode

      if (!materialCode) {
        this.$toast({ content: this.$t('物料编码不能为空'), type: 'warning' })
        return
      }

      try {
        this.loading = true
        const res = await this.$API.costCalculation.queryMdmProperty({
          materialCode,
          categoryCode,
          factoryCode
        })

        if (res.code === 200 && res.data) {
          this.showMdmPropertyDialog(res.data)
        }
      } catch (error) {
        console.error('查询MDM因子属性失败:', error)
        this.$toast({ content: error.msg || this.$t('查询失败'), type: 'error' })
      } finally {
        this.loading = false
      }
    },

    // 显示MDM因子属性弹框
    showMdmPropertyDialog(data) {
      this.$dialog({
        modal: () => import('./components/mdmPropertyDialog.vue'),
        data: {
          title: this.$t('查看因子属性'),
          data: data
        }
      })
    },

    // 获取配置列、表单信息
    async getCostModelDetailColumns() {
      const res = await this.$API.costModel.getCostModelDetailColumns({
        costModelId: this.formObject.costModelId
      })
      if (res.code === 200 && res.data) {
        this.formateColumns(res.data)
      }
    },
    // 处理动态配置信息
    formateColumns(obj) {
      const { extra_formula, form, header, classifyCode } = obj
      const typeMap = {
        0: 'number',
        1: 'text',
        2: 'select'
      }
      // 成本模板分类
      const cmtClassifyCode = classifyCode[0]?.classifyCode

      // 附加动态表单信息
      this.extraFormItems = []
      this.extraFormula = extra_formula || []
      extra_formula?.forEach((item) => {
        const {
          id,
          columnCode,
          columnName,
          columnType,
          columnAlias,
          valueSet,
          calculationFormula,
          calculationFormulaSpec
        } = item
        this.extraFormItems.push({
          id,
          type: typeMap[item.columnType],
          fieldCode: columnCode,
          fieldName: columnName,
          columnAlias,
          columnType,
          valueSet,
          calculationFormula,
          calculationFormulaSpec,
          readonly: true,
          dataSource: []
        })
        if (this.$route.query.type === 'costModel') {
          this.extraFormData[item.columnCode] = calculationFormulaSpec
        }
      })

      // 通用动态表单信息
      this.commonFormItems = []
      form?.forEach((item) => {
        this.commonFormItems.push({
          type: typeMap[item.columnType],
          fieldCode: item.columnCode,
          fieldName: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          valueSet: item.valueSet,
          readonly: !this.enableCalculate
        })
      })

      // 动态列信息
      this.dynamicColumns = []
      const defaultColumns = cloneDeep(this.defaultColumns)
      // 外发类型，不显示成本列
      if (cmtClassifyCode === 'out_going') {
        defaultColumns.splice(-2, 1)
      }
      header?.forEach((item) => {
        this.dynamicColumns.push({
          field: item.columnCode,
          title: item.columnName,
          columnAlias: item.columnAlias,
          columnType: item.columnType,
          minWidth: 150,
          editRender: {
            enabled: item.columnCode !== 'priceUnit' //价格单位不可编辑
          },
          slots: {
            header: () => {
              return [
                <vxe-tooltip content={item.columnCode} enterable>
                  <span>{item.columnName}</span>
                </vxe-tooltip>
              ]
            },
            default: ({ row, level }) => {
              const tip =
                level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
              let template = [<span>{row[item.columnCode]}</span>]
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              if (cmtClassifyCode === 'out_going' && item.columnCode === 'amount') {
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            },
            edit: ({ row, column, level }) => {
              let template = [<span>{row[item.columnCode]}</span>]

              // 成本因子，自动带出规格、品牌、单位、单价
              // const costFactorCol = header?.find((t) => t.columnCode === 'costFactor')
              // const arr = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
              // const colDisabled = costFactorCol && arr.includes(item.columnCode)

              const cellEditable =
                this.$route.query.type === 'calculation' ? !row[item.columnCode + 'Ro'] : true

              // 成本构成为‘其他/它XXX’、costFactor的isInput=1，成本因子可选择、可输入
              const isInput =
                item.valueSet === 'cost_factor' &&
                ([this.$t('其他'), this.$t('其它')].includes(row.nodeName?.substr(0, 2)) ||
                  row.costFactorIsInput === 1)

              // 仅末级可编辑动态列（成本测算，要同时满足单元格标识xxxRo为false）
              if (!row.itemList?.length && cellEditable) {
                switch (typeMap[item.columnType]) {
                  case 'select':
                    template = [
                      <div style='display: flex'>
                        <vxe-input
                          v-model={row[item.columnCode]}
                          clearable
                          readonly={!isInput}
                          type='search'
                          on-clear={() => this.handleClearItem(row, column, item.valueSet)}
                          on-search-click={() => this.handleSelectItem(row, column, item.valueSet)}
                          on-input={({ value }) => {
                            if (item.valueSet === 'cost_factor') {
                              row[item.columnCode + 'Json'] = value
                                ? {
                                    costFactorCode: value,
                                    costFactorName: value
                                  }
                                : null
                            }
                          }}
                        />
                      </div>
                    ]
                    break
                  case 'number':
                  case 'text':
                    template = [
                      <vxe-input
                        v-model={row[item.columnCode]}
                        type={typeMap[item.columnType]}
                        min='0'
                        clearable
                      />
                    ]
                    break
                  default:
                    template = [<span>{row[item.columnCode]}</span>]
                    break
                }
              }
              // 外发类型，非末级金额列，显示为“合计/小计XXXXX”
              else if (cmtClassifyCode === 'out_going' && item.columnCode === 'amount') {
                const tip =
                  level === 0 ? this.$t('合计：') : row.itemList?.length ? this.$t('小计：') : ''
                template = [
                  <span>
                    {tip}
                    {row[item.columnCode]}
                  </span>
                ]
              }
              return template
            }
          }
        })
      })
      // 成本测算，提示信息显示在table表的提示信息列
      if (this.enableCalculate) {
        const column = {
          field: 'errorMsg',
          title: this.$t('提示信息'),
          minWidth: 150,
          slots: {
            default: ({ row }) => {
              return [<span style='color: red'>{row.errorMsg}</span>]
            }
          }
        }
        // 成本测算，增加“提示信息”列
        defaultColumns.push(column)
      }
      defaultColumns.splice(3, 0, ...this.dynamicColumns)
      this.columns = defaultColumns
    },
    // 查询成本模型、成本测算、成本测算历史列表
    async getDataList(type) {
      let funcName = this.enableCalculate ? 'queryEstimate' : 'queryRfxItemCostModel'
      if (type === 'history') {
        funcName = 'queryHistoryEstimate'
      }
      const params = {
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId
      }
      this.loading = true
      const res = await this.$API.costCalculation[funcName](params).catch(() => {
        this.loading = false
      })
      this.loading = false
      if (res?.code === 200) {
        if (type === 'history') {
          this.historyCountList = []
          for (let i = 0; i < 3 && i < res.data.length; i++) {
            this.historyCountList.push({
              text: this.$t(`最近${i + 1}次：${res.data[i].result}`),
              value: i
            })
          }
          this.historyDataList = res.data
        } else {
          this.$emit('updateFormData', res.data || {})
          this.resData = res.data || {}
          this.extraFormData = this.serializeFormData(res.data?.extraList || [])
          this.commonFormData = this.serializeFormData(res.data?.formValueList || [], 'common')
          this.tableData = this.serializeItemList(res.data?.itemList || [])
          this.$nextTick(() => this.tableRef.setAllTreeExpand(true))

          this.isContentChange = false
        }
      }
    },
    // 清除动态列（选择弹窗）
    handleClearItem(row, column, valueSet) {
      row[column.field] = null
      row[column.field + 'Json'] = {}
      row.isUpdate = true

      // 成本因子，带出规格、品牌、单位、单价
      if (
        valueSet === 'cost_factor' &&
        ![this.$t('其他'), this.$t('其它')].includes(row.nodeName?.substr(0, 2)) &&
        row.costFactorIsInput !== 1
      ) {
        const tempList = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
        tempList.forEach((field) => {
          row[field] = null
          if (field === 'unitName') {
            row.unitNameJson = {}
          }
        })
      }

      this.handleUpdateRow(row)
    },
    // 选择动态列（选择弹窗）
    handleSelectItem(row, column, valueSet) {
      let comp = import('@/views/common/components/dialog/itemCodeDialog.vue')
      if (valueSet === 'cost_factor') {
        comp = import('@/views/common/components/dialog/costFactorPriceDialog.vue')
      }
      this.$dialog({
        modal: () => comp,
        data: {
          title: column.title,
          type: 'Cost',
          rfxId: this.$route.query.rfxId,
          valueSet,
          nodeCode: row?.nodeCode
        },
        success: (data) => {
          const oldCellValue = row[column.field]
          const fields = this.getJsonValueFields(valueSet)
          const newValue = data[fields.valueCode]
          const jsonValue = {}
          jsonValue[fields.valueCode] = data[fields.valueCode]
          jsonValue[fields.nameCode] = data[fields.nameCode]

          if (newValue !== oldCellValue) {
            row[column.field] = newValue
            row[column.field + 'Json'] = jsonValue
            row.isUpdate = true

            // 成本因子，带出规格、品牌、单位、单价
            if (valueSet === 'cost_factor') {
              const tempList = [
                { field: 'costFactorSpec', valueKey: 'costFactorSpec' },
                { field: 'costFactorBrand', valueKey: 'costFactorBrand' },
                { field: 'unitName', valueKey: 'basicMeasureUnitCode' },
                { field: 'price', valueKey: 'unitPriceUntaxed' }
              ]
              tempList.forEach((item) => {
                row[item.field] = data[item.valueKey]
                if (item.field === 'unitName') {
                  row.unitNameJson = {
                    unitCode: data.basicMeasureUnitCode,
                    unitName: data.basicMeasureUnitName
                  }
                }
              })
            }
            this.handleUpdateRow(row)
          }
        },
        close: () => {
          this.tableRef.setEditRow(row)
        }
      })
    },
    // 成本模型
    async hanldeSaveCostModel() {
      const { costModelId, costModelCode, costModelName, costModelVersionCode } = this.formObject
      const params = {
        id: this.resData?.id,
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId,
        costModelId,
        costModelCode,
        costModelName,
        costModelVersionCode,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList('common'),
        itemList: this.formatItemList(this.tableData)
      }
      const res = await this.$API.costCalculation.saveRfxItemCostModel(params)
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.getDataList()
      }
    },
    // 成本测算
    async handleCalculate(e) {
      const { row } = e
      let isRowUpdate = row ? this.tableRef.isUpdateByRow(row) : true
      // 解决弹窗编辑单元格，更新之后isUpdateByRow方法获取为false的问题，但实际值更新了（用row的isUpdate属性标识行是否被更新）
      if (row && row.isUpdate) {
        isRowUpdate = true
      }
      this.isContentChange = isRowUpdate
      // 未启用成本测算、行未更新，不进行成本测算
      if (!this.enableCalculate || !isRowUpdate) {
        return
      }

      const { costModelCode, costModelVersionCode } = this.formObject
      const params = {
        id: this.resData?.id,
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId,
        costModelCode,
        versionCode: costModelVersionCode,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList('common'),
        itemList: this.formatItemList(this.tableData)
      }

      this.loading = true
      const res = await this.$API.costCalculation
        .costEstimate(params)
        .catch(() => (this.loading = false))
      this.loading = false
      if (res.code === 200) {
        const expandRows = this.tableRef.getTreeExpandRecords()
        this.estimateRes = res.data
        this.extraFormData = this.serializeFormData(res.data?.extraList || [])
        this.commonFormData = this.serializeFormData(res.data?.formValueList || [], 'common')
        this.tableData = this.serializeItemList(res.data?.itemList || [])

        // 仅展开原本展开的行
        expandRows.forEach((row) => {
          const node = XEUtils.findTree(this.tableData, (r) => r.id === row.id, {
            children: 'itemList'
          })?.item
          if (node) {
            this.$nextTick(() => this.tableRef.setTreeExpand(node, true))
          }
        })
      }
    },
    // 保存、提交成本测算
    async handleEstimate(type) {
      const { costModelCode, costModelVersionCode } = this.formObject
      const params = {
        ...this.resData,
        ...this.estimateRes,
        rfxHeaderId: this.$route.query.rfxId,
        rfxItemId: this.$route.query.rfxItemId,
        costModelCode,
        versionCode: costModelVersionCode,
        extraList: this.formatFormValueList('extra'),
        formValueList: this.formatFormValueList('common'),
        itemList: this.formatItemList(this.tableData)
      }
      const res = await this.$API.costCalculation[type + 'Estimate'](params).catch(
        () => (this.loading = false)
      )
      this.loading = false
      if (res.code === 200) {
        this.$toast({ content: this.$t('操作成功'), type: 'success' })
        this.isContentChange = false
        this.estimateRes = {}
        this.getDataList()
        this.getDataList('history')
        if (type === 'submit') {
          localStorage.setItem('isSubmitCalculation', true)
        }
      }
    },
    // 导入
    handleImport() {
      this.requestUrls = {
        templateUrlPre: 'costCalculation',
        uploadUrl: 'importEstimate',
        id: this.resData?.id,
        noDown: true
      }
      this.showUploadExcel(true)
    },
    // 导出
    async handleExport() {
      const res = await this.$API.costCalculation.exportEstimate({
        id: this.resData?.id
      })
      if (res.data) {
        this.$toast({
          type: 'success',
          content: this.$t('正在导出，请稍后！')
        })
        let fileName = getHeadersFileName(res)
        download({ fileName: fileName, blob: res.data })
      }
    },
    // 递归处理，新增项，自定id义置空
    resetNewItemId(treeList) {
      const resList = treeList.map((item) => {
        return {
          ...item,
          id: item.isAdd ? null : item.id,
          nodeCode: item.isAdd ? null : item.nodeCode,
          itemList: item.itemList ? this.resetNewItemId(item.itemList) : []
        }
      })
      return resList
    },
    // 处理成本测算参数
    formatFormValueList(type) {
      const formValueList = []
      if (type === 'extra') {
        this.extraFormItems.forEach((item) => {
          const { id, fieldCode, fieldName, calculationFormula, columnAlias, columnType } = item
          formValueList.push({
            id,
            fieldCode,
            fieldName,
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            columnType,
            calculationFormula,
            result: this.extraFormData[fieldCode]
          })
        })
      } else {
        this.commonFormItems.forEach((item) => {
          const { fieldCode, fieldName, columnAlias, columnType } = item
          formValueList.push({
            fieldCode,
            fieldName,
            columnCode: fieldCode,
            columnName: fieldName,
            columnAlias,
            columnType,
            dataValue: columnType === 0 ? this.commonFormData[fieldCode] : null,
            stringValue: columnType === 1 ? this.commonFormData[fieldCode] : null,
            jsonValue: columnType === 2 ? this.commonFormData[fieldCode + 'Json'] : null
          })
        })
      }
      return formValueList
    },
    // 递归处理，成本测算参数
    formatItemList(treeList) {
      // 成本因子列
      const costFactorCol = this.dynamicColumns?.find((t) => t.field === 'costFactor')
      const resList = treeList.map((item) => {
        const itemValueList = []
        this.dynamicColumns.forEach((c) => {
          const { field, title, columnAlias, columnType } = c
          let dataValue = null
          if (columnType === 0 && (item[field] || item[field] === '0')) {
            dataValue = Number(item[field])
          }

          let ro = item[field + 'Ro']
          if (this.$route.query.type === 'costModel') {
            // 成本因子，自动带出规格、品牌、单位、单价，不需要设置ro标识是否修改
            const arr = ['costFactorSpec', 'costFactorBrand', 'unitName', 'price']
            ro =
              costFactorCol && arr.includes(field) ? null : this.tableRef.isUpdateByRow(item, field)
          }

          itemValueList.push({
            columnCode: field,
            columnName: title,
            columnAlias,
            columnType,
            dataValue,
            stringValue: columnType === 1 ? item[field] : null,
            jsonValue: columnType === 2 ? item[field + 'Json'] : null,
            ro
          })
        })
        return {
          ...item,
          itemList: item.itemList ? this.formatItemList(item.itemList) : [],
          itemValueList
        }
      })
      return resList
    },
    // 序列化，成本测算结果-表单
    serializeFormData(dataList, type) {
      const formData = {}
      if (type === 'common') {
        dataList.forEach((item) => {
          const { columnType } = item
          let _value = null
          if (columnType === 0) {
            _value = item.dataValue
          } else if (columnType === 1) {
            _value = item.stringValue
          } else {
            const key = Object.keys(item.jsonValue || {})?.find((k) => k?.includes('Code'))
            _value = key ? item.jsonValue[key] : null
          }
          formData[item.columnCode] = _value
        })
      } else {
        dataList.forEach((item) => {
          formData[item.columnCode] = item.result
        })
      }
      return formData
    },
    // 序列化，成本测算结果-列表
    serializeItemList(treeList) {
      const resList = treeList.map((item) => {
        item.itemValueList?.forEach((v) => {
          if (v.columnType === 0) {
            item[v.columnCode] = v.dataValue
          } else if (v.columnType === 1) {
            item[v.columnCode] = v.stringValue
          } else {
            const key = Object.keys(v.jsonValue || {})?.find((k) => k?.includes('Code'))
            item[v.columnCode] = key ? v.jsonValue[key] : null
            item[v.columnCode + 'Json'] = key ? v.jsonValue : null
          }
          item[v.columnCode + 'Ro'] = v.ro
          item[v.columnCode + 'IsInput'] = v.isInput
        })
        return {
          ...item,
          itemList: item.itemList ? this.serializeItemList(item.itemList) : []
        }
      })
      return resList
    },
    // 拖动行，调整层级
    treeDrop() {
      this.$nextTick(() => {
        const xTable = this.tableRef
        this.sortable2 = Sortable.create(
          xTable.$el.querySelector('.body--wrapper>.vxe-table--body tbody'),
          {
            handle: '.drag-btn',
            onEnd: ({ item, oldIndex }) => {
              const options = { children: 'itemList' }

              const targetTrElem = item
              const wrapperElem = targetTrElem.parentNode
              const prevTrElem = targetTrElem.previousElementSibling
              const tableTreeData = this.tableData
              const selfRow = xTable.getRowNode(targetTrElem).item
              const selfNode = XEUtils.findTree(tableTreeData, (row) => row === selfRow, options)
              if (prevTrElem) {
                // 移动到节点
                const prevRow = xTable.getRowNode(prevTrElem).item
                const prevNode = XEUtils.findTree(tableTreeData, (row) => row === prevRow, options)

                if (
                  XEUtils.findTree(selfRow[options.children], (row) => prevRow === row, options)
                ) {
                  // 错误的移动
                  const oldTrElem = wrapperElem.children[oldIndex]
                  wrapperElem.insertBefore(targetTrElem, oldTrElem)
                  return this.$toast({
                    content: this.$t('不允许自己给自己拖动'),
                    type: 'warning'
                  })
                }
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                if (xTable.isTreeExpandByRow(prevRow)) {
                  // 移动到当前的子节点
                  prevRow[options.children].splice(0, 0, currRow)
                } else {
                  // 移动到相邻节点
                  prevNode.items.splice(
                    prevNode.index + (selfNode.index < prevNode.index ? 0 : 1),
                    0,
                    currRow
                  )
                }
              } else {
                // 移动到第一行
                const currRow = selfNode.items.splice(selfNode.index, 1)[0]
                tableTreeData.unshift(currRow)
              }
              // 如果变动了树层级，需要刷新数据
              this.tableData = [...tableTreeData]
              this.isContentChange = true
            }
          }
        )
      })
    },
    // 展示/不展示 上传弹窗
    showUploadExcel(flag) {
      if (flag) {
        this.$refs.uploadExcelRef.uploadData = [] // 清空数据
        this.$refs.uploadExcelRef.$refs.uploader.files = []
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.show()
      } else {
        this.$refs.uploadExcelRef.$refs.dialog.$refs.ejsRef.hide()
      }
    },
    // 导入成功
    upExcelConfirm() {
      this.showUploadExcel(false)
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-content {
  background: #fff !important;
  overflow-x: hidden !important;
  height: calc(100vh - 200px);

  .common-form {
    padding: 10px 0;
    .extra-tip {
      margin: 5px 5px 0 5px;
      color: red;
      font-weight: bold;
    }
    .common-title {
      padding: 0 0 5px 5px;
      font-weight: bold;
      font-size: 15px;
    }
    .common-body {
      padding: 5px 10px 10px 10px;
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      .mt-form {
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, calc(100% / 6 - 10px));
        justify-content: space-between;
        grid-gap: 5px;
      }
    }
  }
}

::v-deep {
  .mt-form-item {
    margin-bottom: 0;
  }
  .mt-input {
    width: 100%;
    height: 28px;
    line-height: 28px;
  }
  .vxe-button {
    margin: 0 10px 0 0;
  }
  .type--text {
    padding: 0;
  }
  .vxe-input {
    width: 100%;
  }
  .vxe-select {
    width: 100%;
  }
}
.sortable-tree-demo .drag-btn {
  cursor: move;
  font-size: 12px;
}
.sortable-tree-demo .vxe-body--row.sortable-ghost,
.sortable-tree-demo .vxe-body--row.sortable-chosen {
  background-color: #dfecfb;
}
</style>
