import Vue from 'vue'
import './vxetable.scss'
import XEUtils from 'xe-utils'
import ColumnConfig from './columnConfig.vue'
import { API } from '@mtech-common/http'
import Sortable from 'sortablejs'

import {
  // 全局对象
  Edit,
  Validator,
  List,
  Pulldown,
  VXETable,
  Icon,
  Column,
  Grid,
  Toolbar,
  Input,
  Button,
  Tooltip,
  Select,
  Table,
  Pager,
  Footer,
  Switch,
  Checkbox
} from 'vxe-table'
import En from 'vxe-table/lib/locale/lang/en-US'
// 按需加载的方式默认是不带国际化的，自定义国际化需要自行解析占位符 '{0}'，例如：
VXETable.setup({
  i18n: (key, args) => XEUtils.toFormatString(XEUtils.get(En, key), args)
})
Vue.use(Icon)
  .use(Validator)
  .use(Edit)
  .use(Column)
  .use(Grid)
  .use(Toolbar)
  .use(Input)
  .use(Button)
  .use(Tooltip)
  .use(Select)
  .use(Table)
  .use(Pager)
  .use(Footer)
  .use(Switch)
  .use(Checkbox)
  .use(List)
  .use(Pulldown)
  .use(Table)
export default {
  name: 'ScTable',
  components: {
    ColumnConfig
  },
  props: {
    height: {
      type: [String, Number],
      default: '100%'
    },
    // 容器高度
    fixHeight: {
      type: Number,
      default: 0
    },
    size: {
      type: String,
      default: 'small'
    },
    rowId: {
      type: String,
      default: 'id'
    },
    tableData: {
      type: Array,
      default() {
        return []
      }
    },
    columns: {
      type: Array,
      default() {
        return []
      }
    },
    columnConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    rowConfig: {
      type: Object,
      default() {
        return {}
      }
    },
    hasDrop: {
      type: Boolean,
      default: false
    },
    customSort: {
      type: Boolean,
      default: false
    },
    watchReserve: {
      type: Boolean,
      default: true
    },
    showOverflow: {
      type: String,
      default: 'tooltip'
    },
    // 显示刷新按钮
    isShowRefreshBth: {
      type: Boolean,
      default: false
    },
    // 显示右侧按钮
    isShowRightBtn: {
      type: Boolean,
      default: true
    },
    // 用于保存列设置
    gridId: {
      type: String,
      default: ''
    },
    // 是否开启列排序
    sortable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tbColumns: [],
      backupData: [],
      containerHeight: '500px',
      saveUserMemoryUrl: '/lowcodeWeb/tenant/user-memory/save',
      getUserMemoryUrl: '/lowcodeWeb/tenant/user-memory/get',
      memoryInfo: {},
      configCloumns: []
      // isFirstLoad: true
    }
  },
  watch: {
    columns: {
      async handler(val) {
        this.serializeColumns(val)
      },
      deep: true,
      immediate: true
    }
  },
  render() {
    const defaultColumnConfig = {
      resizable: true,
      minWidth: '100px',
      useKey: this.rowId,
      ...this.columnConfig
    }
    const defaultRowConfig = {
      isCurrent: true,
      isHover: true,
      height: 32,
      ...this.rowConfig
    }
    const defaultSortConfig = {
      remote: true,
      trigger: 'cell'
    }
    let style = {
      // paddingTop: '16px',
      // height: this.containerHeight,
      background: '#fff'
    }
    let containerHeight = this.fixHeight ? this.fixHeight + 'px' : this.containerHeight
    const currentChangeEvent = ({ row }) => {
      this.$emit('currentChange-event', row)
    }
    return (
      <div class='vex-table-contianer' ref='vxeTableContainer' style={style}>
        <ColumnConfig
          columns={this.configCloumns}
          onChangeColsVisible={this.changeColsVisible}
          onColumnsSequenceChange={this.handleColumnSequenceChange}
          isShowRefreshBth={this.isShowRefreshBth}
          isShowRightBtn={this.isShowRightBtn}
          onRefresh={this.refresh}>
          <template slot='custom-tools'>{this.$slots['custom-tools']}</template>
        </ColumnConfig>
        <div style={{ height: containerHeight, 'min-height': '200px' }}>
          <vxe-grid
            ref='xGrid'
            size={this.size}
            resizable
            height={this.height}
            auto-resize
            rowId={this.rowId}
            stripe
            columns={this.tbColumns}
            column-config={defaultColumnConfig}
            row-config={defaultRowConfig}
            sort-config={this.customSort ? defaultSortConfig : {}}
            show-overflow={this.showOverflow}
            data={this.tableData}
            {...{ scopedSlots: this.$scopedSlots }}
            {...{ attrs: this.$attrs }}
            {...{ on: this.$listeners }}
            border={false}
            header-row-class-name='abcd'
            on-current-change={currentChangeEvent}
            on-resizable-change={this.handleColumnWidthChange}
          />
        </div>
      </div>
    )
  },
  mounted() {
    // 如果不存在fixHeight
    if (!this.fixHeight) {
      this.clickToggle()
      this.clickTab()
      this.getContainerHeight()
    }
    this.columnDrag()
  },
  methods: {
    // 点击toggle
    clickToggle() {
      // 监听但钱页面的toggle(查询条件中的展开、收起)，会影响组件到顶部的距离（distance）
      let eleToggles = document.querySelectorAll('.toggle-container')
      for (let i = 0; i < eleToggles.length; i++) {
        eleToggles[i].addEventListener('click', async () => {
          // 待展开收起完成后切换
          await this.delay(200)
          this.getContainerHeight()
        })
      }
    },
    // 点击Tab
    clickTab() {
      let _this = this
      let eleTab = document.querySelector('.toggle-tab')
      if (!eleTab) return
      let eleContent = eleTab.nextElementSibling
      if (!eleContent.classList?.contains('toggle-content')) return
      let ul = eleTab.querySelector('.tab-container')
      ul.addEventListener('click', async (e) => {
        let target = e.target
        if (['li', 'div'].includes(target?.tagName.toLowerCase())) {
          await _this.delay(100)
          _this.getContainerHeight()
        }
      })
    },
    // 计算表格容器高低
    getContainerHeight() {
      // 获取表格容器元素
      const ele = this.$refs.vxeTableContainer
      // 父节点
      const eleParent = ele?.parentNode
      if (!eleParent) return
      const toolbarEle = ele.querySelector('.table-tool-bar')
      // let pageEle = document.querySelector('.custom-page') // 这样获得的分页dom可能不是当前页的dom
      // tab切换页签通过查找custom-page来获取分页高度
      // let tabPageEle = eleParent.nextElementSibling
      // 获取表格容器距离浏览器顶部距离
      const distance = ele?.getBoundingClientRect().top || 0
      // 按钮栏的高度
      const toolbarEleHeight = toolbarEle?.offsetHeight || 0
      // 分页栏高度
      // const pageEleHeight = pageEle?.offsetHeight || tabPageEle?.offsetHeight || 0
      // 表格得父亲节点得class
      const eleParentClassList = eleParent?.classList
      // 处理弹窗中表格高度自适应
      if (eleParentClassList?.contains('dialog-content')) {
        const parentEle = ele?.parentNode
        const parentHeight = parentEle?.clientHeight
        const children = parentEle?.children || []
        // 兄弟节点的高度
        let sibingsHeight = 0
        for (let i = 0; i < children.length; i++) {
          if (children[i] !== ele) {
            const childHeight = children[i]?.offsetHeight
            sibingsHeight += childHeight
          }
        }
        // 设置表格容器高度
        this.containerHeight = `${parentHeight - sibingsHeight - toolbarEleHeight - 15}px`
      } else {
        // 设置表格容器高度
        this.containerHeight = `calc(100vh - ${distance + toolbarEleHeight + 48}px)`
        // this.containerHeight = `calc(100vh - ${
        //   distance + toolbarEleHeight + pageEleHeight
        // }px - 20px)`
      }
    },
    action() {
      let ret = null
      let funcName = [].shift.call(arguments)
      if (this.$refs.xGrid[funcName]) {
        ret = this.$refs.xGrid[funcName](...arguments)
      }
      return ret
    },
    clearCheckboxRow() {
      this.$refs.xGrid.clearCheckboxRow()
    },
    setActiveRow(row) {
      this.$refs.xGrid.setActiveRow(row)
    },
    setAllTreeExpand(checked) {
      this.$refs.xGrid.setAllTreeExpand(checked)
    },
    changeColsVisible(list) {
      this.tbColumns = list
      this.$emit('changeColsVisible', list)
    },
    refresh() {
      this.$emit('refresh', 'refresh')
    },
    // 获取记忆信息
    async getMemoryInfo() {
      if (!this.gridId) {
        return
      }
      const res = await API.get(this.getUserMemoryUrl, { gridId: this.gridId })
      if (res.code === 200) {
        this.memoryInfo = res.data?.gridMemory || {}
      }
    },
    // 保存记忆信息
    async savetMemoryInfo(gridMemory) {
      if (!this.gridId) {
        return
      }
      const res = await API.post(this.saveUserMemoryUrl, { gridId: this.gridId, gridMemory })
      if (res.code === 200) {
        this.memoryInfo = res.data?.gridMemory || {}
      }
    },
    // 列宽变化
    handleColumnWidthChange(e) {
      const { column, resizeWidth } = e
      if (!column.field) {
        return
      }
      const gridMemory = {
        colWidth: {},
        ...this.memoryInfo
      }
      gridMemory.colWidth[column.field] = resizeWidth
      this.savetMemoryInfo(gridMemory)
    },
    // 列顺序变化
    handleColumnSequenceChange(list) {
      // if (!this.isFirstLoad) {
      const gridMemory = {
        ...this.memoryInfo,
        cols: list
      }
      this.savetMemoryInfo(gridMemory)
      // }
      // this.isFirstLoad = false
    },
    // 处理列配置信息
    async serializeColumns(list) {
      let columns = []
      list.forEach((col) => {
        if (typeof col.visible === 'undefined') {
          col.visible = true
          col.sortable = this.sortable
        }
        if (col.type || col.width || col.width === 0) {
          col.resizable = false
        }
        columns.push(col)
      })

      if (this.gridId) {
        await this.getMemoryInfo()
        const { colWidth, cols } = this.memoryInfo
        // 根据记忆设置列宽
        if (colWidth) {
          columns.forEach((col) => {
            if (col.field && (colWidth[col.field] || colWidth[col.field] === 0)) {
              col.width = this.memoryInfo?.colWidth[col.field]
            }
          })
        }

        const tempColumns = [...columns]

        // 列配置是否被修改过
        let clolumnsIsUpdate = cols?.length !== tempColumns.length
        !clolumnsIsUpdate &&
          tempColumns.forEach((col) => {
            const tempItem = cols.find(
              (c) => (c.field && c.field === col.field) || (c.type && c.type === col.type)
            )
            if (!tempItem) {
              clolumnsIsUpdate = true
            }
          })

        if (clolumnsIsUpdate) {
          // 列配置被修改过，更新记忆信息
          this.handleColumnSequenceChange(columns)
        } else {
          // 列配置未被修改过，按照记忆顺序展示
          columns = []
          cols.forEach((col) => {
            const curItem = tempColumns.find(
              (c) => (c.field && c.field === col.field) || (c.type && c.type === col.type)
            )
            curItem &&
              columns.push({
                ...curItem,
                visible: col.visible
              })
          })
        }
      }
      this.tbColumns = [...columns]
      this.configCloumns = [...columns]
    },
    // 列拖拽
    columnDrag() {
      this.$nextTick(() => {
        const $table = this.$refs.xGrid
        this.sortable2 = Sortable.create(
          $table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'),
          {
            handle: '.vxe-header--column',
            onEnd: ({ item, newIndex, oldIndex }) => {
              const { fullColumn, tableColumn } = $table.getTableColumn()
              const targetThElem = item
              const wrapperElem = targetThElem.parentNode
              const newColumn = fullColumn[newIndex]
              if (newColumn.fixed) {
                const oldThElem = wrapperElem.children[oldIndex]
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(targetThElem, oldThElem)
                } else {
                  wrapperElem.insertBefore(
                    targetThElem,
                    oldThElem ? oldThElem.nextElementSibling : oldThElem
                  )
                }
                this.$toast({ content: this.$t('固定列不允许拖动！'), type: 'warning' })
                return
              }
              // 获取列索引 columnIndex > fullColumn
              const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
              const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
              // 移动到目标列
              const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
              fullColumn.splice(newColumnIndex, 0, currRow)
              $table.loadColumn(fullColumn)
              // 保存调整后的顺序
              this.handleColumnSequenceChange(fullColumn)
            }
          }
        )
      })
    },
    // 工具 - 等待
    delay(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms))
    }
  }
}
